# frozen_string_literal: true

require "open-uri"
module Admin
  class ArticlesController < Admin::AdminController
    skip_before_action :verify_authenticity_token, only: :upload_image

    def index
      if params[:q] && params[:q].length > 2
        collection = Article.pagy_search(params[:q])
        @pagy, @articles = pagy_meilisearch(collection, items: 50)
      else
        articles = Article.includes(:categories, :author).order(created_at: :desc)
        @pagy, @articles = pagy(articles, items: 50)
      end
    end

    def new
      @article = Article.new
    end

    def edit
      @article = Article.friendly.find(params[:id])
    end

    def show; end

    def create
      @article = Article.new(article_params)

      if @article.save
        redirect_to edit_admin_article_path(@article), notice: "Uloženo"
      else
        render :new, status: :unprocessable_entity
      end
    end

    def update
      @article = Article.friendly.find(params[:id])

      if @article.update(article_params)
        redirect_to edit_admin_article_path(@article), notice: "Uloženo"
      else
        render :show, status: :unprocessable_entity
      end
    end

    def upload_image
      result = []

      params[:file].each_with_index do |file, index|
        blob = ActiveStorage::Blob.create_and_upload!(
          io: file,
          filename: file.original_filename,
          content_type: file.content_type
        )

        result[index] = { url: url_for(blob.variant(resize_to_limit: [ 850, nil ])), id: blob.id }
      end

      render json: result
    end

    private

    def article_params
      params.require(:article).permit(:title, :content, :perex, :species, :published_at, :author_id, :magazine_id, :other_authors,
                                      :cover, :banner, :display_ads, category_ids: [])
    end
  end
end
