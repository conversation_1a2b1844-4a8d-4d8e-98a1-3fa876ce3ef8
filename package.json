{"name": "app", "private": "true", "dependencies": {"@hotwired/stimulus": "^3.2.2", "@hotwired/turbo-rails": "^7.3.0", "@rails/actiontext": "^7.1.2", "@rails/request.js": "^0.0.8", "esbuild": "^0.19.5", "latinize": "^0.5.0", "mjml": "^4.14.1", "nice-select2": "^2.3.1", "photoswipe": "^5.4.2", "slim-select": "^2.6.0", "stimulus-carousel": "^5.0.1", "stimulus-lightbox": "^3.2.0", "stimulus-popover": "^6.2.0", "tailwindcss-stimulus-components": "^4.0.4", "trix": "^2.0.7"}, "scripts": {"build": "esbuild app/javascript/application.js --bundle --minify --outdir=app/assets/builds && esbuild app/javascript/admin.js --bundle --minify --outdir=app/assets/builds", "build:admin": "esbuild app/javascript/admin.js --bundle --minify --outdir=app/assets/builds", "build:css": "npx tailwindcss -c ./config/tailwind.config.js -i ./app/assets/stylesheets/application.tailwind.css -o ./app/assets/builds/tailwind.css"}}